Stack trace:
Frame         Function      Args
0007FFFF8E80  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFF8E80, 0007FFFF7D80) msys-2.0.dll+0x1FE8E
0007FFFF8E80  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9158) msys-2.0.dll+0x67F9
0007FFFF8E80  000210046832 (000210286019, 0007FFFF8D38, 0007FFFF8E80, 000000000000) msys-2.0.dll+0x6832
0007FFFF8E80  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF8E80  000210068E24 (0007FFFF8E90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF9160  00021006A225 (0007FFFF8E90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFF87480000 ntdll.dll
7FFF85870000 KERNEL32.DLL
7FFF84A10000 KERNELBASE.dll
7FFF86C00000 USER32.dll
7FFF84EC0000 win32u.dll
000210040000 msys-2.0.dll
7FFF87410000 GDI32.dll
7FFF850E0000 gdi32full.dll
7FFF847E0000 msvcp_win.dll
7FFF84EF0000 ucrtbase.dll
7FFF85AE0000 advapi32.dll
7FFF871C0000 msvcrt.dll
7FFF85320000 sechost.dll
7FFF872F0000 RPCRT4.dll
7FFF83A30000 CRYPTBASE.DLL
7FFF85040000 bcryptPrimitives.dll
7FFF872B0000 IMM32.DLL
