use chrono::{DateTime, Local};
use gpui::{
    div, prelude::*, px, rgb, size, App, Application, Bounds, Timer,Context, MouseButton, Window, WindowBounds, WindowOptions
};

struct Counter {
    count: i32,
    current_time: String,
}

impl Counter {
    fn new() -> Self {
        let now: DateTime<Local> = Local::now();
        Self {
            count: 0,
            current_time: now.format("%Y-%m-%d %H:%M:%S").to_string(),
        }
    }

    fn update_time(&mut self) {
        let now: DateTime<Local> = Local::now();
        self.current_time = now.format("%Y-%m-%d %H:%M:%S").to_string();
    }

    fn start_timer(&mut self, cx: &mut Context<Self>) {
        let entity = cx.entity();
        let executor = cx.foreground_executor();

        // cx.background_executor().spawn(async move {
        //     let mut interval = tokio::time::interval(std::time::Duration::from_secs(1));
        //     loop {
        //         interval.tick().await;

        //         // 使用foreground_executor在主线程中执行更新
        //         let update_result = executor.spawn(async move {
        //             entity.update(&mut cx, |counter, cx| {
        //                 counter.update_time();
        //                 cx.notify();
        //             })
        //         }).await;

        //         if update_result.is_err() {
        //             break;
        //         }
        //     }
        // }).detach();
        //
     
            // cx.spawn(|cx: Context<Self>| async move {
            //     loop {
            //         Timer::after(std::time::Duration::from_secs(1)).await;
            //         cx.update(|counter, cx| {
            //             counter.update_time();
            //             cx.notify();
            //         });
            //     }
            // }).detach();
        
        
    }
}

impl Render for Counter {
    fn render(&mut self, _window: &mut Window, cx: &mut Context<Self>) -> impl IntoElement {
        // 每次渲染时更新时间
        self.update_time();

        div()
            .flex()
            .flex_col()
            .w_full()
            .h_full()
            .bg(rgb(0x1a1a1a))
            .justify_center()
            .items_center()
            .on_mouse_move(cx.listener(|_, _, _, cx| {
                cx.notify();
            }))
            .child(
                div()
                    .flex()
                    .flex_col()
                    .gap_4()
                    .bg(rgb(0x2d2d2d))
                    .w(px(400.0))
                    .h(px(300.0))
                    .justify_center()
                    .items_center()
                    .shadow_lg()
                    .border_1()
                    .border_color(rgb(0x4a90e2))
                    .text_xl()
                    .text_color(rgb(0xffffff))
                    .child(
                        div()
                            .flex()
                            .justify_center()
                            .items_center()
                            .text_2xl()
                            .text_color(rgb(0x4a90e2))
                            .child("计数器"),
                    )
                    .child(
                        div()
                            .flex()
                            .justify_center()
                            .items_center()
                            .text_2xl()
                            .text_color(rgb(0x00ff88))
                            .child(format!("{}", self.count)),
                    )
                    .child(
                        div()
                            .flex()
                            .gap_3()
                            .child(
                                div()
                                    .flex()
                                    .justify_center()
                                    .items_center()
                                    .bg(rgb(0xff4444))
                                    .px_4()
                                    .py_2()
                                    .text_color(rgb(0xffffff))
                                    .child("减少")
                                    .on_mouse_down(
                                        MouseButton::Left,
                                        cx.listener(|counter, _event, _phase, cx| {
                                            counter.count -= 1;
                                            cx.notify();
                                        }),
                                    ),
                            )
                            .child(
                                div()
                                    .flex()
                                    .justify_center()
                                    .items_center()
                                    .bg(rgb(0x666666))
                                    .px_4()
                                    .py_2()
                                    .text_color(rgb(0xffffff))
                                    .child("重置")
                                    .on_mouse_down(
                                        MouseButton::Left,
                                        cx.listener(|counter, _event, _phase, cx| {
                                            counter.count = 0;
                                            cx.notify();
                                        }),
                                    ),
                            )
                            .child(
                                div()
                                    .flex()
                                    .justify_center()
                                    .items_center()
                                    .bg(rgb(0x44ff44))
                                    .px_4()
                                    .py_2()
                                    .text_color(rgb(0xffffff))
                                    .child("增加")
                                    .on_mouse_down(
                                        MouseButton::Left,
                                        cx.listener(|counter, _event, _phase, cx| {
                                            counter.count += 1;
                                            cx.notify();
                                        }),
                                    ),
                            ),
                    )
                    .child(
                        div()
                            .flex()
                            .justify_center()
                            .items_center()
                            .text_sm()
                            .text_color(rgb(0xaaaaaa))
                            .child(format!("当前值: {}", self.count)),
                    ),
            )
            .child(
                div()
                    .flex()
                    .justify_center()
                    .items_center()
                    .mt_4()
                    .text_lg()
                    .text_color(rgb(0xffd700))
                    .child(format!("当前时间: {}", self.current_time)),
            )
            .child(
                div()
                    .flex()
                    .justify_center()
                    .items_center()
                    .mt_2()
                    .text_xs()
                    .text_color(rgb(0x888888))
                    .child("💡 移动鼠标或点击按钮查看时间更新"),
            )
    }
}

fn main() {
    Application::new().run(|cx: &mut App| {
        let bounds = Bounds::centered(None, size(px(400.), px(300.0)), cx);
        cx.open_window(
            WindowOptions {
                window_bounds: Some(WindowBounds::Windowed(bounds)),
                ..Default::default()
            },
            |_, cx| {
                cx.new(|cx| {
                    let mut counter = Counter::new();
                    // 启动定时器，每秒更新一次
                    counter.start_timer(cx);
                    counter
                })
            },
        )
        .unwrap();
    });
}
