# GPUI 时间显示应用

一个使用 Rust 编写的实时时钟应用，展示了多种 GUI 框架的实现方式。

## 🚀 项目特性

- **实时时间显示**: 每秒更新的精确时间
- **多种实现方式**: 控制台版本、EGUI图形界面版本、GPUI概念版本
- **美观界面**: 现代化的深色主题设计
- **可定制性**: 支持字体大小、显示格式等设置
- **跨平台支持**: Windows、macOS、Linux

## 📋 版本说明

### 1. 控制台版本 (主程序)
- 文件: `src/main.rs`
- 特点: 简单易用，在终端中显示精美的时钟界面
- 运行: `cargo run`

### 2. EGUI 图形界面版本 (推荐)
- 文件: `examples/egui_clock.rs`
- 特点: 完整的图形界面，支持主题切换、字体调整等功能
- 运行: `cargo run --example egui_clock`

### 3. GPUI 概念版本
- 文件: `examples/gpui_simple.rs`
- 特点: 使用 Zed 编辑器的 GPUI 框架 (实验性)
- 注意: 由于 GPUI API 复杂性，可能需要调试

## 🛠️ 安装与设置

### 系统要求
- Rust 1.70+
- Cargo
- Windows 10+ / macOS 10.14+ / Linux (X11/Wayland)

### 快速开始

1. **克隆项目**
```bash
git clone <repository-url>
cd gpui_now_time
```

2. **运行控制台版本**
```bash
cargo run
```

3. **运行图形界面版本**
```bash
cargo run --example egui_clock
```

### 依赖安装

项目会自动安装以下依赖：
- `chrono`: 时间处理库
- `eframe`: EGUI 框架
- `env_logger`: 日志记录

## 🖥️ 使用说明

### 控制台版本
- 启动后会显示一个精美的边框时钟
- 按 `Ctrl+C` 退出

### EGUI 图形界面版本
- **主界面**: 显示大号时钟和日期
- **侧边栏设置**:
  - 预设主题 (夜间/日间/桌面时钟)
  - 重置设置
  - 关于信息
- **顶部菜单**:
  - 文件菜单 (退出)
  - 视图菜单 (全屏/置顶)
  - 帮助菜单
- **控制选项**:
  - ✅ 显示秒数
  - ✅ 显示日期
  - ✅ 深色主题
  - 🎚️ 字体大小滑块 (24-120px)

## 🔧 故障排除

### "Blocking waiting for file lock on package cache"
```bash
# Windows PowerShell
taskkill /F /IM cargo.exe
Remove-Item "$env:CARGO_HOME\registry\.cargo-lock" -ErrorAction SilentlyContinue
```

### GPUI 版本编译失败
GPUI 框架仍在快速开发中，API 可能频繁变化。建议：
1. 使用控制台版本或 EGUI 版本
2. 检查 GPUI 最新文档
3. 考虑使用 `create-gpui-app` 工具

### 中文显示问题
如果遇到中文字符显示异常，请确保：
- 终端支持 UTF-8 编码
- 系统已安装中文字体

## 🎨 自定义配置

### 修改时间格式
在相应的源文件中找到格式字符串：
```rust
// 24小时制
.format("%H:%M:%S")

// 12小时制
.format("%I:%M:%S %p")

// 日期格式
.format("%Y年%m月%d日 %A")
```

### 自定义颜色主题
EGUI 版本支持主题切换，也可以在代码中修改颜色：
```rust
.text_color(rgb(0x00ff88))  // 绿色时间
.bg(rgb(0x1a1a1a))          // 深灰背景
```

## 📁 项目结构

```
gpui_now_time/
├── src/
│   └── main.rs              # 控制台版本主程序
├── examples/
│   ├── egui_clock.rs        # EGUI 图形界面版本
│   └── gpui_simple.rs       # GPUI 概念版本
├── Cargo.toml               # 项目依赖配置
└── README.md                # 项目文档
```

## 🚀 开发路线图

- [ ] 添加闹钟功能
- [ ] 支持多时区显示
- [ ] 添加定时器/秒表功能
- [ ] 系统托盘集成
- [ ] 桌面小组件模式
- [ ] 自定义字体支持
- [ ] 动画效果

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request！

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [Chrono](https://github.com/chronotope/chrono) - Rust 日期时间库
- [EGUI](https://github.com/emilk/egui) - 纯 Rust 即时模式 GUI
- [GPUI](https://github.com/zed-industries/zed) - Zed 编辑器的 GPU 加速 UI 框架

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 提交 [GitHub Issue](../../issues)
- 发送邮件到项目维护者

---

⭐ 如果这个项目对你有帮助，请给它一个星标！