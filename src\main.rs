use gpui::{
    actions
    , div, hsla, img, prelude::*, px, rgb,
    AppContext, Application, Context, Font, FontFeatures, FontStyle,
    FontWeight, InteractiveElement, InteractiveText, IntoElement, KeyBinding, MouseButton, ParentElement,
    Render, SharedString, StrikethroughStyle, StyledText, TextAlign, TextRun, UnderlineStyle, WindowOptions
    ,
};

use gpui_component::button::Button;
use gpui_component::StyledExt;
use std::path::Path;

actions!(action_namespace, [Enter]);

struct RootView {
    text: SharedString,
}

impl Render for RootView {
    fn render(&mut self, _window: &mut gpui::Window, _cx: &mut Context<Self>) -> impl IntoElement {
        div()
            .flex()
            .rounded_3xl()
            .size_full()
            .justify_around()
            .text_align(TextAlign::Center)
            .items_center()
            // .justify_center()
            .text_3xl()
            .text_color(rgb(0xFFFFFF))
            .child(StyledText::new("ABVCDSDDSDDDD中文D").with_runs(vec![
                TextRun {
                    len: 4,
                    font: Font {
                        family: SharedString::new_static("宋体"),
                        features: FontFeatures::default(),
                        fallbacks: None,
                        weight: FontWeight::default(),
                        style: FontStyle::default(),
                    },
                    color: hsla(0., 1., 0.5, 1.),
                    background_color: None,
                    underline: None,
                    strikethrough: Some(StrikethroughStyle {
                        thickness: px(1.),
                        color: Some(hsla(0., 1., 0.5, 1.)),
                    }),
                },
                TextRun {
                    len: 3,
                    font: Font {
                        family: SharedString::new_static("宋体"),
                        features: FontFeatures::default(),
                        fallbacks: None,
                        weight: FontWeight::default(),
                        style: FontStyle::default(),
                    },
                    color: hsla(120. / 360., 1., 0.5, 0.5),
                    background_color: None,
                    underline: Some(UnderlineStyle {
                        thickness: px(1.),
                        color: Some(hsla(240. / 360., 1., 0.5, 1.)),
                        wavy: true,
                    }),
                    strikethrough: None,
                },
            ]))
            .child(
                div()
                    .flex()
                    .justify_center()
                    .child(
                        InteractiveText::new(
                            "interactive_text_id",
                            StyledText::new("click me点击我"),
                        )
                        .on_click(
                            vec![1..30],
                            |_range_index, _window, _app| {
                                println!("Clicked");
                            },
                        ),
                    )
                    .child("xx")

            )
            .child(
                div()
                    .v_flex()
                    .rounded_2xl()
                    .border_2()
                    .p_8()
                    .border_color(rgb(0xff0033))
                    .border_r_8()
                    .justify_center()
                    .items_center()
                    .on_mouse_down(MouseButton::Left, |event, _win, _app| {
                        println!("click count {:?}", event.click_count);
                    })
                    // .hover(|s| s.bg(rgb(0x112233)))
                    .child(
                        img(Path::new("./assets").join("icon_1024.png"))
                            ,
                    ),
            )
    }
}

fn main() {
    let application = Application::new();
    application.run(move |app| {
        /*  let entity = app.new(|_cx| SomeState {
            some_value: true,

        });
        entity.update(app, |this, cx| {
            this.some_value = !this.some_value;
            println!("some value = {}", this.some_value);
            cx.notify();
        });

        let some_state = entity.read(app);
        println!("some_value = {:?}", some_state.some_value);
        let weak_entity = entity.downgrade();*/

        let r = app.open_window(WindowOptions::default(), |win, app| {
            app.bind_keys([KeyBinding::new("enter", Enter, None)]);
            app.on_action(|&Enter, _app| {
                println!("Enter hit!");
            });
            println!("Window Bounds = {:?}", win.bounds());
            println!("mouse position = {:?}", win.mouse_position());
            app.new(|_cx| RootView {
                text: SharedString::new_static("hello"),
            })
        });
        match r {
            Ok(wh) => {
                println!("ok")
            }
            Err(message) => {
                println!("err {}", message);
            }
        }
    })
}
