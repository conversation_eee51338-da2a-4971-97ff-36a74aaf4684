
use gpui::{
    div, prelude::*, px, rgb, size, App, Application, Bounds, Context, MouseButton, Window,
    WindowBounds, WindowOptions,
};

struct Counter {
    count: i32,
}

impl Render for Counter {
    fn render(&mut self, _window: &mut Window, cx: &mut Context<Self>) -> impl IntoElement {
        div()
            .flex()
            .flex_col()
            .gap_4()
            .bg(rgb(0x2d2d2d))
            .size(px(400.0))
            .justify_center()
            .items_center()
            .shadow_lg()
            .border_1()
            .border_color(rgb(0x4a90e2))
            .text_xl()
            .text_color(rgb(0xffffff))
            .child(
                div()
                    .flex()
                    .justify_center()
                    .items_center()
                    .text_2xl()
                    .text_color(rgb(0x4a90e2))
                    .child("计数器")
            )
            .child(
                div()
                    .flex()
                    .justify_center()
                    .items_center()
                    .text_2xl()
                    .text_color(rgb(0x00ff88))
                    .child(format!("{}", self.count))
            )
            .child(
                div()
                    .flex()
                    .gap_3()
                    .child(
                        div()
                            .flex()
                            .justify_center()
                            .items_center()
                            .bg(rgb(0xff4444))
                            .px_4()
                            .py_2()
                            .text_color(rgb(0xffffff))
                            .child("减少")
                            .on_mouse_down(MouseButton::Left, cx.listener(|counter, _event, _phase, cx| {
                                counter.count -= 1;
                                cx.notify();
                            }))
                    )
                    .child(
                        div()
                            .flex()
                            .justify_center()
                            .items_center()
                            .bg(rgb(0x666666))
                            .px_4()
                            .py_2()
                            .text_color(rgb(0xffffff))
                            .child("重置")
                            .on_mouse_down(MouseButton::Left, cx.listener(|counter, _event, _phase, cx| {
                                counter.count = 0;
                                cx.notify();
                            }))
                    )
                    .child(
                        div()
                            .flex()
                            .justify_center()
                            .items_center()
                            .bg(rgb(0x44ff44))
                            .px_4()
                            .py_2()
                            .text_color(rgb(0xffffff))
                            .child("增加")
                            .on_mouse_down(MouseButton::Left, cx.listener(|counter, _event, _phase, cx| {
                                counter.count += 1;
                                cx.notify();
                            }))
                    )
            )
            .child(
                div()
                    .flex()
                    .justify_center()
                    .items_center()
                    .text_sm()
                    .text_color(rgb(0xaaaaaa))
                    .child(format!("当前值: {}", self.count))
            )
    }
}

fn main() {
    Application::new().run(|cx: &mut App| {
        let bounds = Bounds::centered(None, size(px(400.), px(300.0)), cx);
        cx.open_window(
            WindowOptions {
                window_bounds: Some(WindowBounds::Windowed(bounds)),
                ..Default::default()
            },
            |_, cx| {
                cx.new(|_| Counter {
                    count: 0,
                })
            },
        )
        .unwrap();
    });
}